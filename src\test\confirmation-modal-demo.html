<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Beautiful Confirmation Modal Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        
        .modal-backdrop {
            backdrop-filter: blur(8px);
            animation: fadeIn 0.2s ease-out;
        }
        
        .modal-content {
            animation: slideUp 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        
        .icon-bounce {
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% {
                transform: translate3d(0,0,0);
            }
            40%, 43% {
                transform: translate3d(0, -8px, 0);
            }
            70% {
                transform: translate3d(0, -4px, 0);
            }
            90% {
                transform: translate3d(0, -2px, 0);
            }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="max-w-4xl mx-auto p-8">
        <div class="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">🎨 Beautiful Confirmation Modals</h1>
            <p class="text-gray-600 mb-8">Modern, accessible confirmation dialogs that replace ugly browser alerts</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Blacklist User -->
                <div class="bg-gradient-to-br from-yellow-50 to-orange-50 p-6 rounded-xl border border-yellow-200">
                    <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-2">Blacklist User</h3>
                    <p class="text-sm text-gray-600 mb-4">Suspend user account with detailed warning</p>
                    <button onclick="showBlacklistModal()" class="w-full bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        Try Blacklist
                    </button>
                </div>

                <!-- Restore Access -->
                <div class="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-xl border border-green-200">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-2">Restore Access</h3>
                    <p class="text-sm text-gray-600 mb-4">Reactivate suspended user account</p>
                    <button onclick="showUnblacklistModal()" class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        Try Restore
                    </button>
                </div>

                <!-- Delete User -->
                <div class="bg-gradient-to-br from-red-50 to-pink-50 p-6 rounded-xl border border-red-200">
                    <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-2">Delete User</h3>
                    <p class="text-sm text-gray-600 mb-4">Permanently remove user data</p>
                    <button onclick="showDeleteModal()" class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        Try Delete
                    </button>
                </div>
            </div>

            <div class="mt-8 p-6 bg-blue-50 rounded-xl border border-blue-200">
                <h3 class="font-semibold text-blue-900 mb-2">✨ Features</h3>
                <ul class="text-sm text-blue-800 space-y-1">
                    <li>• Beautiful, modern design that matches your app</li>
                    <li>• Smooth animations and transitions</li>
                    <li>• Accessible with keyboard navigation</li>
                    <li>• Clear action descriptions and warnings</li>
                    <li>• Customizable icons and colors</li>
                    <li>• No more ugly browser confirm() dialogs!</li>
                </ul>
            </div>
        </div>

        <div id="results" class="bg-white rounded-xl shadow-lg p-6">
            <h3 class="font-semibold text-gray-900 mb-4">🎯 Test Results</h3>
            <div id="result-log" class="space-y-2 text-sm">
                <p class="text-gray-600">Click the buttons above to test the beautiful confirmation modals!</p>
            </div>
        </div>
    </div>

    <!-- Modal Container -->
    <div id="modal-container"></div>

    <script>
        function logResult(message, type = 'info') {
            const log = document.getElementById('result-log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                success: 'text-green-600',
                error: 'text-red-600',
                info: 'text-blue-600'
            };
            
            const entry = document.createElement('div');
            entry.className = `${colors[type] || 'text-gray-600'}`;
            entry.innerHTML = `[${timestamp}] ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        function createModal(config) {
            const container = document.getElementById('modal-container');
            
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 z-50 overflow-y-auto modal-backdrop';
            modal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            
            modal.innerHTML = `
                <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
                    <div class="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl modal-content">
                        <!-- Header -->
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${config.iconBg}">
                                    <div class="${config.iconColor}">
                                        ${config.icon}
                                    </div>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">${config.title}</h3>
                                    ${config.userName ? `<p class="text-sm text-gray-500">User: <span class="font-medium text-gray-700">${config.userName}</span></p>` : ''}
                                </div>
                            </div>
                            <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>

                        <!-- Content -->
                        <div class="mb-6">
                            <p class="text-gray-700 mb-4">${config.message}</p>
                            
                            ${config.details ? `
                                <div class="p-4 rounded-lg border ${config.border} bg-gray-50">
                                    <p class="text-sm font-medium text-gray-700 mb-2">This will:</p>
                                    <ul class="space-y-1">
                                        ${config.details.map(detail => `
                                            <li class="text-sm text-gray-600 flex items-start">
                                                <span class="text-gray-400 mr-2">•</span>
                                                ${detail}
                                            </li>
                                        `).join('')}
                                    </ul>
                                </div>
                            ` : ''}

                            ${config.warning ? `
                                <div class="mt-4 p-3 ${config.warningBg} border ${config.warningBorder} rounded-lg">
                                    <p class="text-sm ${config.warningText} font-medium">
                                        ${config.warning}
                                    </p>
                                </div>
                            ` : ''}
                        </div>

                        <!-- Actions -->
                        <div class="flex space-x-3 justify-end">
                            <button onclick="closeModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors">
                                ${config.cancelText}
                            </button>
                            <button onclick="confirmAction('${config.action}')" class="px-4 py-2 text-sm font-medium text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors ${config.confirmBtn}">
                                ${config.confirmText}
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            container.appendChild(modal);
            
            // Close on backdrop click
            modal.addEventListener('click', (e) => {
                if (e.target === modal) closeModal();
            });
        }

        function closeModal() {
            const container = document.getElementById('modal-container');
            container.innerHTML = '';
        }

        function confirmAction(action) {
            logResult(`✅ ${action} confirmed - Beautiful modal worked perfectly!`, 'success');
            closeModal();
        }

        function showBlacklistModal() {
            createModal({
                title: 'Blacklist User?',
                message: 'Are you sure you want to blacklist MgMC?',
                userName: 'MgMC',
                action: 'Blacklist',
                confirmText: 'Blacklist',
                cancelText: 'Cancel',
                iconBg: 'bg-yellow-100',
                iconColor: 'text-yellow-600',
                confirmBtn: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500',
                border: 'border-yellow-200',
                icon: `<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                </svg>`,
                details: [
                    'Suspend their account immediately',
                    'Prevent them from accessing the system',
                    'Require admin intervention to restore access'
                ],
                warning: '💡 This action can be reversed later if needed.',
                warningBg: 'bg-yellow-50',
                warningBorder: 'border-yellow-200',
                warningText: 'text-yellow-800'
            });
        }

        function showUnblacklistModal() {
            createModal({
                title: 'Restore User Access?',
                message: 'Are you sure you want to restore access for MgMC?',
                userName: 'MgMC',
                action: 'Access Restored',
                confirmText: 'Restore Access',
                cancelText: 'Cancel',
                iconBg: 'bg-green-100',
                iconColor: 'text-green-600',
                confirmBtn: 'bg-green-600 hover:bg-green-700 focus:ring-green-500',
                border: 'border-green-200',
                icon: `<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>`,
                details: [
                    'Reactivate their account',
                    'Allow them to access the system again',
                    'Restore their previous permissions'
                ]
            });
        }

        function showDeleteModal() {
            createModal({
                title: 'Delete User?',
                message: 'Are you sure you want to delete MgMC?',
                userName: 'MgMC',
                action: 'User Deleted',
                confirmText: 'Delete',
                cancelText: 'Cancel',
                iconBg: 'bg-red-100',
                iconColor: 'text-red-600',
                confirmBtn: 'bg-red-600 hover:bg-red-700 focus:ring-red-500',
                border: 'border-red-200',
                icon: `<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>`,
                details: [
                    'This will permanently remove all user data, reports, and activity history'
                ],
                warning: '⚠️ This action cannot be undone.',
                warningBg: 'bg-red-50',
                warningBorder: 'border-red-200',
                warningText: 'text-red-800'
            });
        }

        // Initialize
        logResult('Beautiful confirmation modal demo loaded! 🎨', 'info');
    </script>
</body>
</html>
