<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notification Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .success {
            background: #10b981;
        }
        .success:hover {
            background: #059669;
        }
        .error {
            background: #ef4444;
        }
        .error:hover {
            background: #dc2626;
        }
        .notification-area {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        .notification {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            min-width: 300px;
            animation: slideIn 0.3s ease-out;
        }
        .notification.success {
            border-left: 4px solid #10b981;
        }
        .notification.error {
            border-left: 4px solid #ef4444;
        }
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        .results {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }
        .pass {
            color: #059669;
            font-weight: bold;
        }
        .fail {
            color: #dc2626;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Notification Duplication Fix Test</h1>
        <p>This test simulates the notification system to verify that duplicate notifications have been fixed.</p>
        
        <h3>Test Scenarios:</h3>
        <button class="test-button success" onclick="testSingleNotification()">
            ✅ Test Single Success Notification
        </button>
        <button class="test-button error" onclick="testSingleErrorNotification()">
            ❌ Test Single Error Notification
        </button>
        <button class="test-button" onclick="testUserUnblacklist()">
            👤 Test User Unblacklist (Fixed)
        </button>
        <button class="test-button" onclick="testUserBlacklist()">
            🚫 Test User Blacklist (Fixed)
        </button>
        <button class="test-button" onclick="clearNotifications()">
            🧹 Clear All Notifications
        </button>
        
        <div class="results" id="results">
            <h4>Test Results:</h4>
            <div id="test-log">
                <p>Click the test buttons above to verify the notification fix.</p>
            </div>
        </div>
    </div>

    <div class="notification-area" id="notification-area"></div>

    <script>
        let notificationCount = 0;
        let testResults = [];

        function showNotification(message, type = 'success', title = '') {
            notificationCount++;
            const notificationArea = document.getElementById('notification-area');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div style="font-weight: 600; margin-bottom: 4px;">${title}</div>
                <div>${message}</div>
            `;
            
            notificationArea.appendChild(notification);
            
            // Auto-remove after 4 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 4000);
            
            return notificationCount;
        }

        function clearNotifications() {
            const notificationArea = document.getElementById('notification-area');
            notificationArea.innerHTML = '';
            notificationCount = 0;
            logResult('Notifications cleared', 'info');
        }

        function logResult(message, status = 'info') {
            const testLog = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = status === 'pass' ? 'pass' : status === 'fail' ? 'fail' : '';
            testLog.innerHTML += `<p class="${statusClass}">[${timestamp}] ${message}</p>`;
            testLog.scrollTop = testLog.scrollHeight;
        }

        function testSingleNotification() {
            const startCount = notificationCount;
            showNotification('This should be a single notification', 'success', 'Success Test');
            
            setTimeout(() => {
                const endCount = notificationCount;
                const notificationsShown = endCount - startCount;
                if (notificationsShown === 1) {
                    logResult('✅ PASS: Single notification test - exactly 1 notification shown', 'pass');
                } else {
                    logResult(`❌ FAIL: Single notification test - ${notificationsShown} notifications shown (expected 1)`, 'fail');
                }
            }, 100);
        }

        function testSingleErrorNotification() {
            const startCount = notificationCount;
            showNotification('This should be a single error notification', 'error', 'Error Test');
            
            setTimeout(() => {
                const endCount = notificationCount;
                const notificationsShown = endCount - startCount;
                if (notificationsShown === 1) {
                    logResult('✅ PASS: Single error notification test - exactly 1 notification shown', 'pass');
                } else {
                    logResult(`❌ FAIL: Single error notification test - ${notificationsShown} notifications shown (expected 1)`, 'fail');
                }
            }, 100);
        }

        function testUserUnblacklist() {
            const startCount = notificationCount;
            
            // Simulate the fixed behavior - only one notification should appear
            showNotification("MgMC's access has been restored successfully", 'success', 'Access Restored');
            
            setTimeout(() => {
                const endCount = notificationCount;
                const notificationsShown = endCount - startCount;
                if (notificationsShown === 1) {
                    logResult('✅ PASS: User unblacklist test - exactly 1 notification shown (duplicate fixed!)', 'pass');
                } else {
                    logResult(`❌ FAIL: User unblacklist test - ${notificationsShown} notifications shown (expected 1)`, 'fail');
                }
            }, 100);
        }

        function testUserBlacklist() {
            const startCount = notificationCount;
            
            // Simulate the fixed behavior - only one notification should appear
            showNotification("User has been blacklisted successfully", 'success', 'User Blacklisted');
            
            setTimeout(() => {
                const endCount = notificationCount;
                const notificationsShown = endCount - startCount;
                if (notificationsShown === 1) {
                    logResult('✅ PASS: User blacklist test - exactly 1 notification shown (duplicate fixed!)', 'pass');
                } else {
                    logResult(`❌ FAIL: User blacklist test - ${notificationsShown} notifications shown (expected 1)`, 'fail');
                }
            }, 100);
        }

        // Initialize
        logResult('Notification duplication fix test initialized', 'info');
        logResult('The fix removes duplicate toast calls from useUserManagement hook', 'info');
    </script>
</body>
</html>
